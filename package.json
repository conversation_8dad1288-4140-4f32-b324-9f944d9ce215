{"name": "solar-earn", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development  next dev", "fast": "cross-env NODE_ENV=development next dev --turbo", "build": "prisma generate && next build", "start": "cross-env NODE_ENV=production next start", "lint": "next lint", "format": "next lint --fix && prettier '**/*.{json,yaml}' --write --ignore-path .gitignore", "check-types": "tsc --noEmit --pretty", "prepare": "husky", "postinstall": "prisma generate", "knip": "knip", "find-deadcode": "ts-prune"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.9.4", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.9.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "5.21.1", "@sentry/nextjs": "^8.35.0", "@solana/spl-token": "^0.4.8", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.35", "@solana/web3.js": "1.95.8", "@tanstack/react-query": "^5.59.16", "@tiptap/core": "^2.9.1", "@tiptap/extension-color": "^2.9.1", "@tiptap/extension-heading": "^2.9.1", "@tiptap/extension-image": "^2.9.1", "@tiptap/extension-link": "^2.9.1", "@tiptap/extension-list-item": "^2.9.1", "@tiptap/extension-placeholder": "^2.9.1", "@tiptap/extension-task-item": "^2.9.1", "@tiptap/extension-task-list": "^2.9.1", "@tiptap/extension-text-style": "^2.9.1", "@tiptap/extension-underline": "^2.9.1", "@tiptap/pm": "^2.9.1", "@tiptap/react": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "@vercel/og": "^0.6.3", "axios": "^1.7.3", "boring-avatars": "^1.10.1", "cloudinary": "^2.5.1", "datauri": "^4.1.0", "dayjs": "^1.11.12", "eslint-plugin-prettier": "^5.2.1", "flag-icons": "^7.2.3", "framer-motion": "^11.11.9", "franc": "^6.2.0", "html-react-parser": "^5.1.18", "husky": "^9.1.4", "immer": "^10.1.1", "jotai": "^2.10.1", "jsonwebtoken": "^9.0.2", "lodash.debounce": "^4.0.8", "micro": "^10.0.1", "next": "14.2.14", "next-auth": "^4.24.10", "next-axiom": "^0.18.1", "next-pwa": "^5.6.0", "next-seo": "^6.5.0", "nextjs-toploader": "^3.7.15", "nodemailer": "^6.9.11", "papaparse": "^5.4.1", "posthog-js": "^1.176.0", "prosemirror-model": "^1.23.0", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.31.4", "react": "18.3.1", "react-canvas-confetti": "^2.0.7", "react-countdown": "^2.3.5", "react-dom": "18.3.1", "react-dropzone": "^14.2.10", "react-hook-form": "^7.53.1", "react-icons": "^5.2.1", "react-intersection-observer": "^9.13.0", "react-select": "^5.8.0", "react-textarea-autosize": "^8.5.4", "resend": "^4.0.0", "sass": "^1.80.4", "sharp": "0.32.6", "slugify": "^1.6.6", "sonner": "^1.5.0", "svix": "^1.37.0", "textarea-caret": "^3.1.0", "tiptap-extension-resize-image": "^1.1.7", "tslog": "^4.9.3", "unfurl.js": "^6.4.0", "unique-names-generator": "^4.7.1", "zod": "^3.23.8", "zustand": "^5.0.0"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.59.7", "@tanstack/react-query-devtools": "^5.59.16", "@types/eslint-config-prettier": "~6.11.3", "@types/jsonwebtoken": "^9.0.6", "@types/lodash.debounce": "~4.0.9", "@types/node": "22.7.9", "@types/papaparse": "^5.3.15", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@types/textarea-caret": "^3.0.3", "@typescript-eslint/eslint-plugin": "^7.11.0", "@typescript-eslint/parser": "^7.11.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-next": "14.2.14", "eslint-config-prettier": "^9.1.0", "eslint-plugin-chakra-ui": "^0.11.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^3.1.0", "lint-staged": "^15.2.8", "prettier": "^3.3.2", "prisma": "5.21.1", "typescript": "5.6.3", "typescript-eslint": "^7.11.0"}, "browserslist": ["Chrome >= 100.0.4896.127", "Firefox >= 115", "Safari >= 14.1.2"], "pnpm": {"overrides": {"prosemirror-model": "^1.19.0"}}}