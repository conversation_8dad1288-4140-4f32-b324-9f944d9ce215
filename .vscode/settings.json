{
  "editor.tabSize": 2,
  "editor.detectIndentation": false,
  "search.exclude": {
    "package-lock.json": true
  },
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.addMissingImports": "explicit",
    "source.fixAll.eslint": "explicit"
  },
  "jest.autoRun": {
    "watch": false // Start the jest with the watch flag
    // "onStartup": ["all-tests"] // Run all tests upon project launch
  },
  "jest.showCoverageOnLoad": true, // Show code coverage when the project is launched
  "jest.showTerminalOnLaunch": false, // Don't automatically open test explorer terminal on launch
  // Multiple language settings for json and jsonc files
  "[json][jsonc][yaml]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[prisma]": {
    "editor.defaultFormatter": "Prisma.prisma"
  },
  "prettier.ignorePath": ".gitignore",
  "cSpell.words": ["CLOUDINARY"] // Don't run prettier for files listed in .gitignore
}
