import { type SkillMap } from '@/interface/skills';

import { Superteams } from './Superteam';

export { chinaArea } from './chinaArea';
export { countries } from './country';
export * from './exclusiveSponsors';

export interface MultiSelectOptions {
  value: string;
  label: string;
}

export const skillMap: SkillMap[] = [
  {
    mainskill: 'Growth',
    color: '#16A368',
  },
  {
    mainskill: 'Frontend',
    color: '#3E8BFF',
  },
  {
    mainskill: 'Backend',
    color: '#FF833E',
  },
  {
    mainskill: 'Blockchain',
    color: '#FF3EC9',
  },
  {
    mainskill: 'Design',
    color: '#7E51FF',
  },
  {
    mainskill: 'Content',
    color: '#5EA8BF',
  },
  {
    mainskill: 'Community',
    color: '#EA580C',
  },
  {
    mainskill: 'Mobile',
    color: '#7E51FF',
  },
  {
    mainskill: 'Other',
    color: '#64758B',
  },
];

export const IndustryList = [
  'DAO 组织',
  '去中心化金融',
  '基础设施',
  'DePIN',
  '消费级应用',
  '游戏',
  '社交',
  'PayFi',
  'RWA',
  'Meme',
  '其他',
];

interface Token {
  tokenName: string;
  tokenSymbol: string;
  mintAddress: string;
  icon: string;
  decimals: number;
  coingeckoSymbol: string;
}

export const tokenList: Token[] = [
  {
    tokenName: 'USDT',
    tokenSymbol: 'USDT',
    mintAddress: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/825.png',
    decimals: 6,
    coingeckoSymbol: 'tether',
  },
  {
    tokenName: 'USDC',
    tokenSymbol: 'USDC',
    mintAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/128x128/3408.png',
    decimals: 6,
    coingeckoSymbol: 'usd-coin',
  },
  {
    tokenName: 'Solana (SOL)',
    tokenSymbol: 'SOL',
    mintAddress: 'So11111111111111111111111111111111111111111',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/16116.png',
    decimals: 9,
    coingeckoSymbol: 'solana',
  },
  {
    tokenName: 'JUP',
    tokenSymbol: 'JUP',
    mintAddress: 'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/29210.png',
    decimals: 6,
    coingeckoSymbol: 'jupiter-exchange-solana',
  },
  {
    tokenName: 'BONK',
    tokenSymbol: 'BONK',
    mintAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
    icon: 'https://assets.coingecko.com/coins/images/28600/standard/bonk.jpg?1696527587',
    decimals: 5,
    coingeckoSymbol: 'bonk',
  },
  {
    tokenName: 'ISC',
    tokenSymbol: 'ISC',
    mintAddress: 'J9BcrQfX4p9D1bvLzRNCbMDv8f44a9LFdeqNE4Yk2WMD',
    icon: 'https://res.cloudinary.com/dgvnuwspr/image/upload/v1683200072/sponsors/International%20Stable%20Currency.png',
    decimals: 6,
    coingeckoSymbol: 'international-stable-currency',
  },
  {
    tokenName: 'STEP',
    tokenSymbol: 'STEP',
    mintAddress: 'StepAscQoEioFxxWGnh2sLBDFp9d8rvKz2Yp39iDpyT',
    icon: 'https://assets.coingecko.com/coins/images/14988/small/step.png?1619274762',
    decimals: 9,
    coingeckoSymbol: 'step-finance',
  },
  {
    tokenName: 'Pyth Network (PYTH)',
    tokenSymbol: 'PYTH',
    mintAddress: 'HZ1JovNiVvGrGNiiYvEozEVgZ58xaU3RKwX8eACQBCt3',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/128x128/28177.png',
    decimals: 6,
    coingeckoSymbol: 'pyth-network',
  },
  {
    tokenName: 'Kamino (KMNO)',
    tokenSymbol: 'KMNO',
    mintAddress: 'KMNo3nJsBXfcpJTVhZcXLW7RmTwTt4GVFE7suUBo9sS',
    icon: '/assets/coins/kamino.png',
    decimals: 6,
    coingeckoSymbol: 'kamino',
  },
  {
    tokenName: 'Jito (JTO)',
    tokenSymbol: 'JTO',
    mintAddress: 'jtojtomepa8beP8AuQc6eXt5FriJwfFMwQx2v2f9mCL',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/128x128/28541.png',
    decimals: 9,
    coingeckoSymbol: 'jito-governance-token',
  },
  {
    tokenName: 'Metaplex (MPLX)',
    tokenSymbol: 'MPLX',
    mintAddress: 'METAewgxyPbgwsseH8T16a39CQ5VyVxZi9zXiDPY18m',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/128x128/21870.png',
    decimals: 6,
    coingeckoSymbol: 'metaplex',
  },
  {
    tokenName: 'Send (SEND)',
    tokenSymbol: 'SEND',
    mintAddress: 'SENDdRQtYMWaQrBroBrJ2Q53fgVuq95CV9UPGEvpCxa',
    icon: 'https://assets.coingecko.com/coins/images/39222/standard/photo1719676966.jpeg?1721182505',
    decimals: 6,
    coingeckoSymbol: 'send-2',
  },
  {
    tokenName: 'PayPal USD (PYUSD)',
    tokenSymbol: 'PYUSD',
    mintAddress: '2b1kV6DkPAnxd5ixfnxCpjxmKwqjjaYmCZfHsFu24GXo',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/128x128/27772.png',
    decimals: 6,
    coingeckoSymbol: 'paypal-usd',
  },
  {
    tokenName: 'Drift',
    tokenSymbol: 'DRIFT',
    mintAddress: 'DriFtupJYLTosbwoN8koMbEYSx54aFAVLddWsbksjwg7',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/128x128/31278.png',
    decimals: 6,
    coingeckoSymbol: 'drift-protocol',
  },
  {
    tokenName: 'SAROS',
    tokenSymbol: 'SAROS',
    mintAddress: 'SarosY6Vscao718M4A778z4CGtvcwcGef5M9MEH1LGL',
    icon: 'https://assets.coingecko.com/coins/images/34594/standard/saros-token-logo.png?1705476813',
    decimals: 6,
    coingeckoSymbol: 'saros-finance',
  },
  {
    tokenName: 'Samoyed Coin (SAMO)',
    tokenSymbol: 'SAMO',
    mintAddress: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
    icon: 'https://assets.coingecko.com/coins/images/15051/standard/IXeEj5e.png?1696514710',
    decimals: 9,
    coingeckoSymbol: 'samoyedcoin',
  },
  {
    tokenName: 'Pax Dollar (USDP)',
    tokenSymbol: 'USDP',
    mintAddress: 'HVbpJAQGNpkgBaYBZQBR1t7yFdvaYVp2vCQQfKKEN4tM',
    icon: 'https://assets.coingecko.com/coins/images/6013/standard/Pax_Dollar.png?1696506427',
    decimals: 6,
    coingeckoSymbol: 'paxos-standard',
  },
  {
    tokenName: 'SynesisOne (SNS)',
    tokenSymbol: 'SNS',
    mintAddress: 'SNSNkV9zfG5ZKWQs6x4hxvBRV6s8SqMfSGCtECDvdMd',
    icon: 'https://assets.coingecko.com/coins/images/23289/standard/sns.png?1696522507',
    decimals: 9,
    coingeckoSymbol: 'synesis-one',
  },
  {
    tokenName: "Dean's List",
    tokenSymbol: 'DEAN',
    mintAddress: 'Ds52CDgqdWbTWsua1hgT3AuSSy4FNx2Ezge1br3jQ14a',
    icon: 'https://assets.coingecko.com/coins/images/36197/standard/logo_dl.png?1710811437',
    decimals: 6,
    coingeckoSymbol: 'dean-s-list',
  },
  {
    tokenName: 'Ore',
    tokenSymbol: 'ORE',
    mintAddress: 'oreoU2P8bN6jkk3jbaiVxYnG1dCXcYxwhwyK9jSybcp',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/128x128/32782.png',
    decimals: 9,
    coingeckoSymbol: 'ore',
  },
  {
    tokenName: 'mSOL',
    tokenSymbol: 'mSOL',
    mintAddress: 'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11461.png',
    decimals: 9,
    coingeckoSymbol: 'msol',
  },
  {
    tokenName: 'UXD Stablecoin (UXD)',
    tokenSymbol: 'UXD',
    mintAddress: '7kbnvuGBxxj8AG9qp8Scn56muWGaRaFqxg1FsRp3PaFT',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/17535.png',
    decimals: 6,
    coingeckoSymbol: 'uxd-stablecoin',
  },
  {
    tokenName: 'Solayer USD (sUSD)',
    tokenSymbol: 'sUSD',
    mintAddress: 'susdabGDNbhrnCa6ncrYo81u4s9GM8ecK2UwMyZiq4X',
    icon: 'https://statics.solscan.io/cdn/imgs/s60?ref=68747470733a2f2f7261772e67697468756275736572636f6e74656e742e636f6d2f736f6c617965722d6c6162732f746f6b656e2d6d657461646174612f6d61696e2f737573642f696d6167652e706e67',
    decimals: 6,
    coingeckoSymbol: 'solayer-usd',
  },
  {
    tokenName: 'Raydium (RAY)',
    tokenSymbol: 'RAY',
    mintAddress: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/8526.png',
    decimals: 6,
    coingeckoSymbol: 'raydium',
  },
  {
    tokenName: 'Saber (SBR)',
    tokenSymbol: 'SBR',
    mintAddress: 'Saber2gLauYim4Mvftnrasomsv6NvAuncvMEZwcLpD1',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11181.png',
    decimals: 6,
    coingeckoSymbol: 'saber',
  },
  {
    tokenName: 'KIWI',
    tokenSymbol: 'KIWI',
    mintAddress: '66Qq2qS67K4L5xQ3xUTinCyxzdPeZQG1R1ipK8jrY7gc',
    icon: 'https://bafkreibcamcjwo5z3itvybznrdtb3fgeiplfy36izu75jygxkt7jzoq4ju.ipfs.nftstorage.link/',
    decimals: 5,
    coingeckoSymbol: 'kiwi-token-2',
  },
  {
    tokenName: 'EUROe Stablecoin (EUROe)',
    tokenSymbol: 'EUROe',
    mintAddress: '2VhjJ9WxaGC3EZFwJG9BDUs9KxKCAjQY4vgd1qxgYWVg',
    icon: 'https://assets.coingecko.com/coins/images/28913/standard/euroe-200x200-round.png?1696527889',
    decimals: 6,
    coingeckoSymbol: 'euroe-stablecoin',
  },
  {
    tokenName: 'Solend (SLND)',
    tokenSymbol: 'SLND',
    mintAddress: 'SLNDpmoWTVADgEdndyvWzroNL7zSi1dF9PC3xHGtPwp',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/13524.png',
    decimals: 6,
    coingeckoSymbol: 'solend',
  },
  {
    tokenName: 'Coin98 (C98)',
    tokenSymbol: 'C98',
    mintAddress: 'C98A4nkJXhpVZNAZdHUA95RpTF3T4whtQubL3YobiUX9',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/10903.png',
    decimals: 6,
    coingeckoSymbol: 'coin98',
  },
  {
    tokenName: 'Serum (SRM)',
    tokenSymbol: 'SRM',
    mintAddress: 'SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6187.png',
    decimals: 6,
    coingeckoSymbol: 'serum',
  },
  {
    tokenName: 'DUST Protocol (DUST)',
    tokenSymbol: 'DUST',
    mintAddress: 'DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/18802.png',
    decimals: 9,
    coingeckoSymbol: 'dust-protocol',
  },
  {
    tokenName: 'wrapped Solana (wSOL)',
    tokenSymbol: 'wSOL',
    mintAddress: 'So11111111111111111111111111111111111111112',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/16116.png',
    decimals: 9,
    coingeckoSymbol: 'wrapped-solana',
  },
  {
    tokenName: 'Bonfida (FIDA)',
    tokenSymbol: 'FIDA',
    mintAddress: 'EchesyfXePKdLtoiZSL8pBe8Myagyy8ZRqsACNCFGnvp',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7978.png',
    decimals: 6,
    coingeckoSymbol: 'bonfida',
  },
  {
    tokenName: 'Orca (ORCA)',
    tokenSymbol: 'ORCA',
    mintAddress: 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11165.png',
    decimals: 6,
    coingeckoSymbol: 'orca',
  },
  {
    tokenName: 'Helium (HNT)',
    tokenSymbol: 'HNT',
    mintAddress: 'hntyVP6YFm1Hg25TN9WGLqM12b8TQmcknKrdu1oxWux',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5665.png',
    decimals: 8,
    coingeckoSymbol: 'helium',
  },
  {
    tokenName: 'Helium Mobile (MOBILE)',
    tokenSymbol: 'MOBILE',
    mintAddress: 'mb1eu7TzEc71KxDpsmsKoucSSuuoGLv1drys1oP2jh6',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/24600.png',
    decimals: 6,
    coingeckoSymbol: 'helium-mobile',
  },
  {
    tokenName: 'Helium IOT (IOT)',
    tokenSymbol: 'IOT',
    mintAddress: 'iotEVVZLEywoTn1QdwNPddxPWszn3zFhEot3MfL9fns',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/24686.png',
    decimals: 6,
    coingeckoSymbol: 'helium-iot',
  },
  {
    tokenName: 'Gary (GARY)',
    tokenSymbol: 'GARY',
    mintAddress: '8c71AvjQeKKeWRe8jtTGG1bJ2WiYXQdbjqFbUfhHgSVk',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/21046.png',
    decimals: 9,
    coingeckoSymbol: 'gary',
  },
  {
    tokenName: 'Blaze (BLZE)',
    tokenSymbol: 'BLZE',
    mintAddress: 'BLZEEuZUBVqFhj8adcCFPJvPVCiCyVmh3hkJMrU8KuJA',
    icon: 'https://assets.coingecko.com/coins/images/28392/standard/blze.png?1696527391',
    decimals: 9,
    coingeckoSymbol: 'solblaze',
  },
  {
    tokenName: 'META (META)',
    tokenSymbol: 'META',
    mintAddress: 'METADDFL6wWMWEoKTFJwcThTbUmtarRJZjRpzUvkxhr',
    icon: 'https://avatars.githubusercontent.com/u/107701386?s=200&v=4',
    decimals: 9,
    coingeckoSymbol: 'meta-2',
  },
  {
    tokenName: 'ZETA (ZEX)',
    tokenSymbol: 'ZEX',
    mintAddress: 'ZEXy1pqteRu3n13kdyh4LwPQknkFk3GzmMYMuNadWPo',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/128x128/32002.png',
    decimals: 6,
    coingeckoSymbol: 'zeta',
  },
  {
    tokenName: 'Degod (DEGOD)',
    tokenSymbol: 'DEGOD',
    mintAddress: 'degod39zqQWzpG6h4b7SJLLTCFE6FeZnZD8BwHBFxaN',
    icon: '/assets/coins/degods.png',
    decimals: 6,
    coingeckoSymbol: 'degod',
  },
  {
    tokenName: 'DevWifHat (DWH)',
    tokenSymbol: 'DWH',
    mintAddress: 'DEVwHJ57QMPPArD2CyjboMbdWvjEMjXRigYpaUNDTD7o',
    icon: '/assets/coins/dwh.png',
    decimals: 6,
    coingeckoSymbol: '',
  },
  {
    tokenName: 'ChiitanCoin (CTAN)',
    tokenSymbol: 'CTAN',
    mintAddress: 'A3gMddXMAhmc3P9dLjHv2n6ywWWzpGZbRCH6y8sta8ug',
    icon: '/assets/coins/ctan.png',
    decimals: 9,
    coingeckoSymbol: '',
  },
  {
    tokenName: 'Utgard (UTG)',
    tokenSymbol: 'UTG',
    mintAddress: 'FPZsqAgtwf58GmJMctZGSu9RaXiGAmQLL6ZtKUUewm7k',
    icon: '/assets/coins/utg.png',
    decimals: 9,
    coingeckoSymbol: '',
  },
  {
    tokenName: 'Vin (VIN)',
    tokenSymbol: 'VIN',
    mintAddress: '6B2X4NmSsmkiT8ytFEVt15igRSgsKNGZ3j3WWeidupE8',
    icon: '/assets/coins/vin.png',
    decimals: 9,
    coingeckoSymbol: '',
  },
];

export const CountryList: string[] = [
  'India',
  'Afghanistan',
  'Åland Islands',
  'Albania',
  'Algeria',
  'American Samoa',
  'Andorra',
  'Angola',
  'Anguilla',
  'Antarctica',
  'Antigua & Barbuda',
  'Argentina',
  'Armenia',
  'Aruba',
  'Australia',
  'Austria',
  'Azerbaijan',
  'Bahamas',
  'Bahrain',
  'Bangladesh',
  'Barbados',
  'Belarus',
  'Belgium',
  'Belize',
  'Benin',
  'Bermuda',
  'Bhutan',
  'Bolivia',
  'Bosnia and Herzegovina',
  'Botswana',
  'Bouvet Island',
  'Brazil',
  'British Indian Ocean Territory',
  'British Virgin Islands',
  'Brunei',
  'Bulgaria',
  'Burkina Faso',
  'Burundi',
  'Cambodia',
  'Cameroon',
  'Canada',
  'Cape Verde',
  'Carribean Netherlands',
  'Cayman Islands',
  'Central African Republic',
  'Chad',
  'Chile',
  'China',
  'Christmas Island',
  'Cocos (Keeling) Islands',
  'Colombia',
  'Comoros',
  'Congo',
  'Cook Islands',
  'Costa Rica',
  'Croatia',
  'Cuba',
  'Curaçao',
  'Cyprus',
  'Czech Republic',
  'Denmark',
  'Djibouti',
  'Dominica',
  'Dominican Republic',
  'East Timor',
  'Ecuador',
  'Egypt',
  'El Salvador',
  'Equatorial Guinea',
  'Eritrea',
  'Estonia',
  'Eswatini',
  'Ethiopia',
  'Falkland Islands (Malvinas)',
  'Faroe Islands',
  'Fiji',
  'Finland',
  'France',
  'French Guiana',
  'French Polynesia',
  'French Southern Territories',
  'Gabon',
  'Gambia',
  'Georgia',
  'Germany',
  'Ghana',
  'Gibraltar',
  'Greece',
  'Greenland',
  'Grenada',
  'Guadeloupe',
  'Guam',
  'Guatemala',
  'Guernsey',
  'Guinea',
  'Guinea-Bissau',
  'Guyana',
  'Haiti',
  'Heard and Mc Donald Islands',
  'Honduras',
  'Hong Kong',
  'Hungary',
  'Iceland',
  'Indonesia',
  'Iran (Islamic Republic of)',
  'Iraq',
  'Ireland',
  'Isle of Man',
  'Israel',
  'Italy',
  'Ivory Coast',
  'Jamaica',
  'Japan',
  'Jersey',
  'Jordan',
  'Kazakhstan',
  'Kenya',
  'Kiribati',
  "Korea, Democratic People's Republic of",
  'Korea, Republic of',
  'Kosovo',
  'Kuwait',
  'Kyrgyzstan',
  'Laos',
  'Latvia',
  'Lebanon',
  'Lesotho',
  'Liberia',
  'Libyan Arab Jamahiriya',
  'Liechtenstein',
  'Lithuania',
  'Luxembourg',
  'Macau',
  'Macedonia',
  'Madagascar',
  'Malawi',
  'Malaysia',
  'Maldives',
  'Mali',
  'Malta',
  'Marshall Islands',
  'Martinique',
  'Mauritania',
  'Mauritius',
  'Mayotte',
  'Mexico',
  'Micronesia, Federated States of',
  'Moldova',
  'Monaco',
  'Mongolia',
  'Montenegro',
  'Montserrat',
  'Morocco',
  'Mozambique',
  'Myanmar',
  'Namibia',
  'Nauru',
  'Nepal',
  'Netherlands',
  'Netherlands Antilles',
  'New Caledonia',
  'New Zealand',
  'Nicaragua',
  'Niger',
  'Nigeria',
  'Niue',
  'Norfolk Island',
  'North Macedonia',
  'Northern Mariana Islands',
  'Norway',
  'Oman',
  'Pakistan',
  'Palau',
  'Palestinian Territories',
  'Panama',
  'Papua New Guinea',
  'Paraguay',
  'Peru',
  'Philippines',
  'Pitcairn',
  'Poland',
  'Portugal',
  'Puerto Rico',
  'Qatar',
  'Reunion',
  'Romania',
  'Russian Federation',
  'Rwanda',
  'Saint Kitts and Nevis',
  'Saint Lucia',
  'Saint Vincent and the Grenadines',
  'Samoa',
  'San Marino',
  'Sao Tome and Principe',
  'Saudi Arabia',
  'Senegal',
  'Serbia',
  'Seychelles',
  'Sierra Leone',
  'Singapore',
  'Sint Maarten',
  'Slovakia',
  'Slovenia',
  'Solomon Islands',
  'Somalia',
  'South Africa',
  'South Georgia South Sandwich Islands',
  'South Sudan',
  'Spain',
  'Sri Lanka',
  'St. Barthélemy',
  'St. Helena',
  'St. Kitts & Nevis',
  'St. Lucia',
  'St. Martin',
  'St. Pierre and Miquelon',
  'St. Vincent & Grenadines',
  'Sudan',
  'Suriname',
  'Svalbard and Jan Mayen Islands',
  'Swaziland',
  'Sweden',
  'Switzerland',
  'Syrian Arab Republic',
  'Taiwan',
  'Tajikistan',
  'Tanzania',
  'Thailand',
  'Timor-Leste',
  'Togo',
  'Tokelau',
  'Tonga',
  'Trinidad and Tobago',
  'Tunisia',
  'Turkey',
  'Turkmenistan',
  'Turks and Caicos Islands',
  'Tuvalu',
  'U.S. Outlying Islands',
  'U.S. Virgin Islands',
  'Uganda',
  'Ukraine',
  'United Arab Emirates',
  'United Kingdom',
  'United States',
  'Uruguay',
  'Uzbekistan',
  'Vanuatu',
  'Vatican City State',
  'Venezuela',
  'Vietnam',
  'Virgin Islands (British)',
  'Virgin Islands (U.S.)',
  'Wallis and Futuna Islands',
  'Western Sahara',
  'Yemen',
  'Yugoslavia',
  'Zaire',
  'Zambia',
  'Zimbabwe',
];

// 看有需求就使用
export const chinaCityList: string[] = [
  '北京市',
  '天津市',
  '石家庄市',
  '唐山市',
  '秦皇岛市',
  '邯郸市',
  '邢台市',
  '保定市',
  '张家口市',
  '承德市',
  '沧州市',
  '廊坊市',
  '衡水市',
  '太原市',
  '大同市',
  '阳泉市',
  '长治市',
  '晋城市',
  '朔州市',
  '晋中市',
  '运城市',
  '忻州市',
  '临汾市',
  '吕梁市',
  '呼和浩特市',
  '包头市',
  '乌海市',
  '赤峰市',
  '通辽市',
  '鄂尔多斯市',
  '呼伦贝尔市',
  '巴彦淖尔市',
  '乌兰察布市',
  '兴安盟',
  '锡林郭勒盟',
  '阿拉善盟',
  '沈阳市',
  '大连市',
  '鞍山市',
  '抚顺市',
  '本溪市',
  '丹东市',
  '锦州市',
  '营口市',
  '阜新市',
  '辽阳市',
  '盘锦市',
  '铁岭市',
  '朝阳市',
  '葫芦岛市',
  '长春市',
  '吉林市',
  '四平市',
  '辽源市',
  '通化市',
  '白山市',
  '松原市',
  '白城市',
  '延边朝鲜族自治州',
  '哈尔滨市',
  '齐齐哈尔市',
  '鸡西市',
  '鹤岗市',
  '双鸭山市',
  '大庆市',
  '伊春市',
  '佳木斯市',
  '七台河市',
  '牡丹江市',
  '黑河市',
  '绥化市',
  '大兴安岭地区',
  '上海市',
  '南京市',
  '无锡市',
  '徐州市',
  '常州市',
  '苏州市',
  '南通市',
  '连云港市',
  '淮安市',
  '盐城市',
  '扬州市',
  '镇江市',
  '泰州市',
  '宿迁市',
  '杭州市',
  '宁波市',
  '温州市',
  '嘉兴市',
  '湖州市',
  '绍兴市',
  '金华市',
  '衢州市',
  '舟山市',
  '台州市',
  '丽水市',
  '合肥市',
  '芜湖市',
  '蚌埠市',
  '淮南市',
  '马鞍山市',
  '淮北市',
  '铜陵市',
  '安庆市',
  '黄山市',
  '滁州市',
  '阜阳市',
  '宿州市',
  '六安市',
  '亳州市',
  '池州市',
  '宣城市',
  '福州市',
  '厦门市',
  '莆田市',
  '三明市',
  '泉州市',
  '漳州市',
  '南平市',
  '龙岩市',
  '宁德市',
  '南昌市',
  '景德镇市',
  '萍乡市',
  '九江市',
  '新余市',
  '鹰潭市',
  '赣州市',
  '吉安市',
  '宜春市',
  '抚州市',
  '上饶市',
  '济南市',
  '青岛市',
  '淄博市',
  '枣庄市',
  '东营市',
  '烟台市',
  '潍坊市',
  '济宁市',
  '泰安市',
  '威海市',
  '日照市',
  '临沂市',
  '德州市',
  '聊城市',
  '滨州市',
  '菏泽市',
  '郑州市',
  '开封市',
  '洛阳市',
  '平顶山市',
  '安阳市',
  '鹤壁市',
  '新乡市',
  '焦作市',
  '濮阳市',
  '许昌市',
  '漯河市',
  '三门峡市',
  '南阳市',
  '商丘市',
  '信阳市',
  '周口市',
  '驻马店市',
  '济源市',
  '武汉市',
  '黄石市',
  '十堰市',
  '宜昌市',
  '襄阳市',
  '鄂州市',
  '荆门市',
  '孝感市',
  '荆州市',
  '黄冈市',
  '咸宁市',
  '随州市',
  '恩施土家族苗族自治州',
  '仙桃市',
  '潜江市',
  '天门市',
  '神农架林区',
  '长沙市',
  '株洲市',
  '湘潭市',
  '衡阳市',
  '邵阳市',
  '岳阳市',
  '常德市',
  '张家界市',
  '益阳市',
  '郴州市',
  '永州市',
  '怀化市',
  '娄底市',
  '湘西土家族苗族自治州',
  '广州市',
  '韶关市',
  '深圳市',
  '珠海市',
  '汕头市',
  '佛山市',
  '江门市',
  '湛江市',
  '茂名市',
  '肇庆市',
  '惠州市',
  '梅州市',
  '汕尾市',
  '河源市',
  '阳江市',
  '清远市',
  '东莞市',
  '中山市',
  '潮州市',
  '揭阳市',
  '云浮市',
  '南宁市',
  '柳州市',
  '桂林市',
  '梧州市',
  '北海市',
  '防城港市',
  '钦州市',
  '贵港市',
  '玉林市',
  '百色市',
  '贺州市',
  '河池市',
  '来宾市',
  '崇左市',
  '海口市',
  '三亚市',
  '三沙市',
  '儋州市',
  '五指山市',
  '琼海市',
  '文昌市',
  '万宁市',
  '东方市',
  '定安县',
  '屯昌县',
  '澄迈县',
  '临高县',
  '白沙黎族自治县',
  '昌江黎族自治县',
  '乐东黎族自治县',
  '陵水黎族自治县',
  '保亭黎族苗族自治县',
  '琼中黎族苗族自治县',
  '重庆市',
  '成都市',
  '自贡市',
  '攀枝花市',
  '泸州市',
  '德阳市',
  '绵阳市',
  '广元市',
  '遂宁市',
  '内江市',
  '乐山市',
  '南充市',
  '眉山市',
  '宜宾市',
  '广安市',
  '达州市',
  '雅安市',
  '巴中市',
  '资阳市',
  '阿坝藏族羌族自治州',
  '甘孜藏族自治州',
  '凉山彝族自治州',
  '贵阳市',
  '六盘水市',
  '遵义市',
  '安顺市',
  '毕节市',
  '铜仁市',
  '黔西南布依族苗族自治州',
  '黔东南苗族侗族自治州',
  '黔南布依族苗族自治州',
  '昆明市',
  '曲靖市',
  '玉溪市',
  '保山市',
  '昭通市',
  '丽江市',
  '普洱市',
  '临沧市',
  '楚雄彝族自治州',
  '红河哈尼族彝族自治州',
  '文山壮族苗族自治州',
  '西双版纳傣族自治州',
  '大理白族自治州',
  '德宏傣族景颇族自治州',
  '怒江傈僳族自治州',
  '迪庆藏族自治州',
  '拉萨市',
  '日喀则市',
  '昌都市',
  '林芝市',
  '山南市',
  '那曲市',
  '阿里地区',
  '西安市',
  '铜川市',
  '宝鸡市',
  '咸阳市',
  '渭南市',
  '延安市',
  '汉中市',
  '榆林市',
  '安康市',
  '商洛市',
  '兰州市',
  '嘉峪关市',
  '金昌市',
  '白银市',
  '天水市',
  '武威市',
  '张掖市',
  '平凉市',
  '酒泉市',
  '庆阳市',
  '定西市',
  '陇南市',
  '临夏回族自治州',
  '甘南藏族自治州',
  '西宁市',
  '海东市',
  '海北藏族自治州',
  '黄南藏族自治州',
  '海南藏族自治州',
  '果洛藏族自治州',
  '玉树藏族自治州',
  '海西蒙古族藏族自治州',
  '银川市',
  '石嘴山市',
  '吴忠市',
  '固原市',
  '中卫市',
  '乌鲁木齐市',
  '克拉玛依市',
  '吐鲁番市',
  '哈密市',
  '昌吉回族自治州',
  '博尔塔拉蒙古自治州',
  '巴音郭楞蒙古自治州',
  '阿克苏地区',
  '克孜勒苏柯尔克孜自治州',
  '喀什地区',
  '和田地区',
  '伊犁哈萨克自治州',
  '塔城地区',
  '阿勒泰地区',
  '石河子市',
  '阿拉尔市',
  '图木舒克市',
  '五家渠市',
  '北屯市',
  '铁门关市',
  '双河市',
  '可克达拉市',
  '昆玉市',
  '胡杨河市',
  '新星市',
  '白杨市',
];

const superteams = Superteams.map((team) => team.name);

export const CommunityList: string[] = [
  ...superteams,
  'SuperWomenDao',
  'Other',
];

export const web3Exp = [
  '刚接触Web3，正在学习相关知识',
  '有时参与Web3相关项目或社区，有一定了解',
  '持续、定期在Web3领域工作或贡献，有较深经验',
];

export const workExp = ['0年', '少于2年', '2-5年', '5-9年', '9年以上'];
export const workType = ['不找工作', '自由职业', '全职', '实习'];

export const MAX_COMMENT_SUGGESTIONS = 5;

export const TERMS_OF_USE = '/terms.pdf';

export const URL_REGEX = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i;

export const MAX_PODIUMS = 10;
export const MAX_BONUS_SPOTS = 50;
export const BONUS_REWARD_POSITION = 99;

export const PDTG = 'https://t.me/cryptosheep1/';

export const SolarMail = '<EMAIL>';
