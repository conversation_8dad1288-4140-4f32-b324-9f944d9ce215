import { Button, Flex, useDisclosure } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import Image from 'next/image';
import { useSession } from 'next-auth/react';
import { usePostHog } from 'posthog-js/react';
import React, { useState, useEffect } from 'react';
import { LuPencil } from 'react-icons/lu';

import { Tooltip } from '@/components/shared/responsive-tooltip';
import { SurveyModal } from '@/components/shared/Survey';
import { AuthWrapper } from '@/features/auth';
import {
  getListingDraftStatus,
  getRegionTooltipLabel,
  isDeadlineOver,
  type Listing,
  userRegionEligibilty,
} from '@/features/listings';
import { useUser } from '@/store/user';

import { userSubmissionQuery } from '../../queries/user-submission-status';
import { EasterEgg } from './EasterEgg';
import { SubmissionModal } from './SubmissionModal';

interface Props {
  listing: Listing;
  isTemplate?: boolean;
}

export const SubmissionActionButton = ({
  listing,
  isTemplate = false,
}: Props) => {
  const {
    id,
    status,
    isPublished,
    deadline,
    region,
    type,
    isWinnersAnnounced,
    Hackathon,
  } = listing;

  const [isEasterEggOpen, setEasterEggOpen] = useState(false);
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const { user } = useUser();

  const { status: authStatus } = useSession();

  const isAuthenticated = authStatus === 'authenticated';

  const isUserEligibleByRegion = userRegionEligibilty(region, user?.location);

  const { data: submissionStatus, isLoading: isUserSubmissionLoading } =
    useQuery({
      ...userSubmissionQuery(id!, user?.id),
      enabled: isAuthenticated,
      // Add timeout to prevent infinite loading
      meta: {
        errorPolicy: 'soft'
      }
    });

  const isSubmitted = submissionStatus?.isSubmitted ?? false;

  // Handle long loading states to prevent permanent stuck state
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isUserSubmissionLoading) {
      // If loading for more than 10 seconds, show timeout state
      timeoutId = setTimeout(() => {
        setLoadingTimeout(true);
      }, 10000);
    } else {
      setLoadingTimeout(false);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isUserSubmissionLoading]);

  const posthog = usePostHog();

  const { isOpen, onOpen, onClose } = useDisclosure();

  const regionTooltipLabel = getRegionTooltipLabel(region);

  const bountyDraftStatus = getListingDraftStatus(status, isPublished);

  const pastDeadline = isDeadlineOver(deadline) || isWinnersAnnounced;
  const buttonState = getButtonState();

  const handleSubmit = () => {
    if (buttonState === 'submit') {
      posthog.capture('start_submission');
    } else if (buttonState === 'edit') {
      posthog.capture('edit_submission');
    }
    onOpen();
  };

  const hackathonStartDate = Hackathon?.startDate
    ? dayjs(Hackathon?.startDate)
    : null;

  const hasHackathonStarted = hackathonStartDate
    ? dayjs().isAfter(hackathonStartDate)
    : true;

  const isProject = type === 'project';

  let buttonText;
  let buttonBG;
  let buttonTextColor;
  let isBtnDisabled;
  let btnLoadingText;

  function getButtonState() {
    if (isSubmitted && !pastDeadline) return 'edit';
    if (isSubmitted && pastDeadline) return 'submitted';
    return 'submit';
  }

  switch (buttonState) {
    case 'edit':
      buttonText = isProject ? '修改' : '修改';
      isBtnDisabled = false;
      btnLoadingText = null;
      break;

    case 'submitted':
      buttonText = isProject
        ? 'Applied Successfully'
        : 'Submitted Successfully';
      buttonBG = 'green.500';
      isBtnDisabled = true;
      btnLoadingText = null;
      break;

    default:
      buttonText = isProject ? '立即申请' : '现在提交';
      if (
        listing.compensationType === 'variable' ||
        listing.compensationType === 'range'
      )
        buttonText = '发送报价';
      buttonBG = 'brand.purple';
      isBtnDisabled = Boolean(
        pastDeadline ||
        (user?.id &&
          user?.isTalentFilled &&
          ((bountyDraftStatus !== 'PUBLISHED' && status !== 'PREVIEW') ||
            !hasHackathonStarted ||
            !isUserEligibleByRegion)),
      );
      btnLoadingText = '正在检查中';
  }
  if (isDeadlineOver(deadline) && !isWinnersAnnounced) {
    buttonText = '提交正在审核中';
    buttonBG = 'gray.500';
  } else if (isWinnersAnnounced) {
    buttonText = '获胜者已公布';
    buttonBG = 'gray.500';
  }

  const {
    isOpen: isSurveyOpen,
    onOpen: onSurveyOpen,
    onClose: onSurveyClose,
  } = useDisclosure();

  // https://posthog.com/surveys
  const surveyId = 'testid-meaningless'; // TODO

  return (
    <>
      {isOpen && (
        <SubmissionModal
          id={id}
          onClose={onClose}
          isOpen={isOpen}
          editMode={buttonState === 'edit'}
          listing={listing}
          isTemplate={isTemplate}
          showEasterEgg={() => setEasterEggOpen(true)}
          onSurveyOpen={onSurveyOpen}
        />
      )}
      {isSurveyOpen &&
        (!user?.surveysShown || !(surveyId in user.surveysShown)) && (
          <SurveyModal
            isOpen={isSurveyOpen}
            onClose={onSurveyClose}
            surveyId={surveyId}
          />
        )}
      {isEasterEggOpen && (
        <EasterEgg
          isOpen={isEasterEggOpen}
          onClose={() => setEasterEggOpen(false)}
          isProject={isProject}
        />
      )}
      <Image
        // Hack to show GIF Immediately when Easter Egg is visible
        src="/assets/memes/jiesuan.gif"
        style={{
          width: '100%',
          marginTop: 'auto',
          display: 'block',
          visibility: 'hidden',
          position: 'fixed',
          zIndex: -99999,
          top: '-300%',
          left: '-300%',
        }}
        width="500"
        height="600"
        priority
        loading="eager"
        quality={80}
      />

      <Flex
        className="ph-no-capture"
        pos={{ base: 'fixed', md: 'static' }}
        zIndex={999}
        bottom={0}
        left="50%"
        w="full"
        px={{ base: 3, md: 0 }}
        py={{ base: 4, md: 0 }}
        bg="white"
        transform={{ base: 'translateX(-50%)', md: 'none' }}
      >
        <AuthWrapper
          showCompleteProfileModal
          completeProfileModalBodyText={
            '请在提交前填写个人资料'
          }
          style={{ w: 'full', cursor: 'pointer' }}
        >
          <Tooltip
            bg="brand.slate.500"
            hasArrow
            isDisabled={
              hasHackathonStarted && (isUserEligibleByRegion || pastDeadline)
            }
            label={
              !isUserEligibleByRegion
                ? regionTooltipLabel
                : !hasHackathonStarted
                  ? `This track will open for submissions on ${hackathonStartDate?.format('DD MMMM, YYYY')}`
                  : ''
            }
            rounded="md"
          >
            <Button
              gap={4}
              w={'full'}
              mb={{ base: 12, md: 5 }}
              textColor={buttonTextColor}
              bg={buttonBG}
              _hover={{ bg: buttonBG }}
              _disabled={{ opacity: '70%' }}
              isDisabled={isBtnDisabled}
              isLoading={isUserSubmissionLoading && !loadingTimeout}
              loadingText={loadingTimeout ? '加载超时，请刷新页面' : btnLoadingText}
              onClick={handleSubmit}
              size="lg"
              variant={buttonState === 'edit' ? 'outline' : 'solid'}
            >
              {buttonState === 'edit' && <LuPencil />}
              {buttonText}
            </Button>
          </Tooltip>
        </AuthWrapper>
      </Flex>
    </>
  );
};
