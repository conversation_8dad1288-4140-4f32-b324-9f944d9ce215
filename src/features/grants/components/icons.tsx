import React from 'react';

const TimeToPayIcon = () => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.3951 12.6473V8.31062C14.4159 8.20358 14.3754 7.95001 14.0428 7.79412C13.7529 7.65799 13.4369 7.85856 13.3424 7.9895C11.8978 10.2987 9.19793 15.156 9.09297 15.3992C8.95995 15.7079 9.0514 15.9001 9.17611 16.0415C9.27171 16.1475 9.49619 16.2108 9.61674 16.2108H12.5505L11.9249 21.5161C11.9405 21.6312 11.9922 21.7385 12.0724 21.8224C12.1527 21.9064 12.2574 21.9629 12.3717 21.9838C12.7074 22.0648 12.9392 21.8227 13.0129 21.6907L17.809 13.5764C17.8776 13.4694 18.0231 13.1846 17.8693 12.9352C17.8103 12.8426 17.7279 12.7672 17.6305 12.7167C17.533 12.6661 17.424 12.6422 17.3143 12.6473H14.3951Z"
        fill="#6366F1"
      />
    </svg>
  );
};

const DollarIcon = () => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.41663 17H11.25C11.25 17.99 12.5058 18.8333 14 18.8333C15.4941 18.8333 16.75 17.99 16.75 17C16.75 15.9917 15.7966 15.625 13.78 15.1392C11.8366 14.6533 9.41663 14.0483 9.41663 11.5C9.41663 9.85917 10.7641 8.46583 12.625 7.99833V6H15.375V7.99833C17.2358 8.46583 18.5833 9.85917 18.5833 11.5H16.75C16.75 10.51 15.4941 9.66667 14 9.66667C12.5058 9.66667 11.25 10.51 11.25 11.5C11.25 12.5083 12.2033 12.875 14.22 13.3608C16.1633 13.8467 18.5833 14.4517 18.5833 17C18.5833 18.6408 17.2358 20.0342 15.375 20.5017V22.5H12.625V20.5017C10.7641 20.0342 9.41663 18.6408 9.41663 17Z"
        fill="#6366F1"
      />
    </svg>
  );
};

const PayoutIcon = () => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.3483 10H7.65189C7.46482 10 7.28541 10.0743 7.15313 10.2066C7.02085 10.3389 6.94653 10.5183 6.94653 10.7054V20.5803C6.94653 20.7674 7.02085 20.9468 7.15313 21.0791C7.28541 21.2114 7.46482 21.2857 7.65189 21.2857H20.3483C20.5353 21.2857 20.7148 21.2114 20.847 21.0791C20.9793 20.9468 21.0536 20.7674 21.0536 20.5803V10.7054C21.0536 10.5183 20.9793 10.3389 20.847 10.2066C20.7148 10.0743 20.5353 10 20.3483 10ZM19.6429 17.7589C19.0817 17.7589 18.5435 17.9818 18.1466 18.3787C17.7498 18.7755 17.5269 19.3138 17.5269 19.875H10.4733C10.4733 19.3138 10.2504 18.7755 9.85353 18.3787C9.45669 17.9818 8.91846 17.7589 8.35724 17.7589V13.5268C8.91846 13.5268 9.45669 13.3038 9.85353 12.907C10.2504 12.5102 10.4733 11.9719 10.4733 11.4107H17.5269C17.5269 11.9719 17.7498 12.5102 18.1466 12.907C18.5435 13.3038 19.0817 13.5268 19.6429 13.5268V17.7589Z"
        fill="#6366F1"
      />
      <path
        d="M14 12.8214C12.444 12.8214 11.1786 14.0868 11.1786 15.6428C11.1786 17.1988 12.444 18.4643 14 18.4643C15.556 18.4643 16.8214 17.1988 16.8214 15.6428C16.8214 14.0868 15.556 12.8214 14 12.8214ZM14 17.0535C13.222 17.0535 12.5893 16.4208 12.5893 15.6428C12.5893 14.8648 13.222 14.2321 14 14.2321C14.778 14.2321 15.4107 14.8648 15.4107 15.6428C15.4107 16.4208 14.778 17.0535 14 17.0535Z"
        fill="#6366F1"
      />
    </svg>
  );
};

export { DollarIcon, PayoutIcon, TimeToPayIcon };
