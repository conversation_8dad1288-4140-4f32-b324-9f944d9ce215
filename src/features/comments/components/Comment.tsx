import {
  Alert,
  AlertDescription,
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  AlertIcon,
  AlertTitle,
  Button,
  Collapse,
  Fade,
  Flex,
  HStack,
  Link,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Text,
  useDisclosure,
  useMediaQuery,
  VStack,
} from '@chakra-ui/react';
import { type CommentRefType } from '@prisma/client';
import axios from 'axios';
import Image from 'next/image';
import NextLink from 'next/link';
import { usePostHog } from 'posthog-js/react';
import { useEffect, useRef, useState } from 'react';

import { AuthWrapper } from '@/features/auth';
import { EarnAvatar } from '@/features/talent';
import { type Comment as IComment } from '@/interface/comments';
import { type User } from '@/interface/user';
import { useUser } from '@/store/user';
import { dayjs } from '@/utils/dayjs';
import { getURL } from '@/utils/validUrl';

import { formatFromNow } from '../utils';
import { CommentParser } from './CommentParser';
import { UserSuggestionTextarea } from './UserSuggestionTextarea';

interface Props {
  comment: IComment;
  poc: User | undefined;
  refId: string;
  refType: CommentRefType;
  sponsorId: string | undefined;
  defaultSuggestions: Map<string, User>;
  deleteComment: (commentId: string) => Promise<void>;
  listingSlug: string;
  listingType: string;
  isAnnounced: boolean;
  isReply?: boolean;
  addNewReply?: (msg: string) => Promise<void>;
  isVerified?: boolean;
  isTemplate?: boolean;
}

export const Comment = ({
  comment,
  sponsorId,
  refId,
  refType,
  poc,
  deleteComment,
  defaultSuggestions,
  addNewReply,
  listingType,
  listingSlug,
  isReply = false,
  isAnnounced,
  isVerified = false,
  isTemplate = false,
}: Props) => {
  const { user } = useUser();
  const posthog = usePostHog();

  const {
    isOpen: deleteIsOpen,
    onOpen: deleteOnOpen,
    onClose: deleteOnClose,
  } = useDisclosure();
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [showReplies, setShowReplies] = useState(false);
  const [replies, setReplies] = useState(comment?.replies ?? []);
  const [newReply, setNewReply] = useState('');
  const [newReplyLoading, setNewReplyLoading] = useState(false);
  const [newReplyError, setNewReplyError] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteError, setDeleteError] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const cancelRef = useRef<any>(null);

  useEffect(() => {
    const reply = localStorage.getItem(`comment-${refId}-${comment.id}`);
    if (reply) {
      setNewReply(reply);
      setShowReplies(true);
      setShowReplyInput(true);
      localStorage.removeItem(`comment-${refId}-${comment.id}`);
    }
  }, []);

  const deleteReplyLvl1 = async (replyId: string) => {
    posthog.capture('delete_comment');
    const replyIndex = replies.findIndex((reply) => reply.id === replyId);
    if (replyIndex > -1) {
      await axios.delete(`/api/comment/${replyId}/delete`);
      setReplies((prevReplies) => {
        const newReplies = [...prevReplies];
        newReplies.splice(replyIndex, 1);
        return newReplies;
      });
    } else {
      throw new Error('Reply not found');
    }
  };

  const handleDelete = async () => {
    setDeleteLoading(true);
    setDeleteError(false);
    try {
      await deleteComment(comment.id);
      setDeleteLoading(false);
      deleteOnClose();
    } catch (e) {
      console.log('error - ', e);
      setDeleteError(true);
      setDeleteLoading(false);
    }
  };

  const addNewReplyLvl1 = async (msg: string) => {
    posthog.capture('publish_comment');
    setNewReplyError(false);
    const newReplyData = await axios.post(`/api/comment/create`, {
      message: msg,
      refType: refType,
      refId: refId,
      replyToId: comment?.id ?? null,
      replyToUserId: comment?.authorId ?? null,
      pocId: poc?.id,
    });
    setReplies((prevReplies) => [...prevReplies, newReplyData.data]);
    setShowReplies(true);
  };

  const date = formatFromNow(dayjs(comment?.updatedAt).fromNow());

  const handleSubmit = async () => {
    try {
      setNewReplyLoading(true);
      setNewReplyError(false);

      if (addNewReply) {
        await addNewReply(newReply);
      } else {
        await addNewReplyLvl1(newReply);
      }

      setNewReply('');
      setNewReplyLoading(false);
      setShowReplyInput(false);
    } catch (e) {
      console.log('error - ', e);
      setNewReplyError(true);
      setNewReplyLoading(false);
    }
  };

  useEffect(() => {
    localStorage.setItem(`comment-${refId}-${comment.id}`, newReply);
  }, [newReply]);

  const [isMobile] = useMediaQuery('(max-width: 768px)');

  return (
    <>
      <HStack
        key={comment.id}
        align="start"
        gap={3}
        overflow="visible"
        w="full"
        onMouseEnter={() => {
          if (!isMobile) setShowOptions(true);
        }}
        onMouseLeave={() => {
          if (!isMobile) setShowOptions(false);
        }}
      >
        <Link
          as={NextLink}
          href={`${getURL()}t/${comment?.author?.username}`}
          style={{
            minWidth: isReply ? '28px' : '36px',
            maxWidth: isReply ? '28px' : '36px',
          }}
          tabIndex={-1}
          target="_blank"
        >
          <EarnAvatar
            size={isReply ? '28px' : '36px'}
            id={comment?.author?.id}
            avatar={comment?.author?.photo}
          />
        </Link>

        <VStack align={'start'} gap={0} w="100%">
          <HStack align="end" gap={2}>
            <Link
              as={NextLink}
              href={`${getURL()}t/${comment?.author?.username}`}
              tabIndex={-1}
              target="_blank"
            >
              <Text
                color="brand.slate.800"
                fontSize={{
                  base: 'sm',
                  md: 'md',
                }}
                fontWeight={500}
              >
                {`${comment?.author?.firstName} ${comment?.author?.lastName}`}
              </Text>
            </Link>
            {comment?.author?.currentSponsorId === sponsorId && (
              <Text
                gap={0.5}
                display="flex"
                pb="2px"
                color="blue.500"
                fontSize={{
                  base: 'xs',
                  md: 'sm',
                }}
                fontWeight={500}
              >
                {isVerified && (
                  <Image
                    width={13}
                    height={13}
                    src="/assets/icons/verified-tick.svg"
                  />
                )}
                项目方
              </Text>
            )}
            <Text
              pb="2px"
              color="brand.slate.400"
              fontSize={{
                base: 'xs',
                md: 'sm',
              }}
              fontWeight={500}
            >
              {date}
            </Text>
          </HStack>
          <Text
            overflow={'clip'}
            maxW={{
              base: '15rem',
              sm: '20rem',
              md: '17rem',
              lg: '29rem',
              xl: '46rem',
            }}
            mt={'0px !important'}
            color="brand.slate.500"
            fontSize={{
              base: 'sm',
              md: 'md',
            }}
          >
            <CommentParser
              listingSlug={listingSlug}
              listingType={listingType}
              isAnnounced={isAnnounced}
              type={comment.type}
              submissionId={comment.submissionId}
              value={comment?.message}
            />
          </Text>
          <HStack overflow="visible!important" pt={2}>
            {replies?.length > 0 && (
              <Button
                pos="relative"
                left="-3px"
                color="brand.purple.dark"
                fontSize={{
                  base: 'xs',
                  md: 'sm',
                }}
                fontWeight={500}
                bg="none"
                onClick={() => setShowReplies((prev) => !prev)}
                variant="link"
              >
                <svg
                  style={{ marginRight: '4px' }}
                  width="7"
                  height="4"
                  viewBox="0 0 7 4"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0.375 0.25L3.5 3.375L6.625 0.25H0.375Z"
                    fill="#4F46E5"
                  />
                </svg>
                {replies?.length} 回复
              </Button>
            )}
            <Button
              left={'-3px'}
              color="brand.slate.500"
              fontSize={{
                base: 'xs',
                md: 'sm',
              }}
              fontWeight={500}
              bg="none"
              onClick={() => setShowReplyInput((prev) => !prev)}
              variant="link"
            >
              回复
            </Button>
          </HStack>
          <Collapse
            animateOpacity
            in={showReplyInput}
            style={{ width: '100%', overflow: 'visible!important' }}
          >
            <VStack gap={4} w={'full'} mb={4} pt={4}>
              <Flex gap={3} w="full">
                <EarnAvatar size={'28px'} id={user?.id} avatar={user?.photo} />
                <UserSuggestionTextarea
                  autoFocusOn={showReplyInput}
                  defaultSuggestions={defaultSuggestions}
                  pt={0}
                  fontSize={{
                    base: 'sm',
                    md: 'md',
                  }}
                  borderColor="brand.slate.200"
                  _placeholder={{
                    color: 'brand.slate.400',
                  }}
                  focusBorderColor="brand.purple"
                  placeholder=""
                  value={newReply}
                  setValue={setNewReply}
                  variant="flushed"
                />
              </Flex>
              {!!newReplyError && (
                <Text my={0} mt={4} color="red" fontSize="xs">
                  添加评论时出错！请重试！
                </Text>
              )}
              <Collapse
                animateOpacity
                in={!!newReply}
                style={{ width: '100%', overflow: 'visible!important' }}
                unmountOnExit={true}
              >
                <Flex justify={'end'} gap={4} w="full">
                  <AuthWrapper
                    showCompleteProfileModal
                    completeProfileModalBodyText={'参与任务前请先完善个人信息.'}
                  >
                    <Button
                      h="auto"
                      px={5}
                      py={2}
                      color="brand.slate.800"
                      fontSize={{
                        base: 'xs',
                      }}
                      fontWeight={500}
                      bg="brand.slate.200"
                      _hover={{
                        bg: 'brand.slate.300',
                      }}
                      _active={{
                        bg: 'brand.slate.400',
                      }}
                      isDisabled={!!newReplyLoading || !newReply || isTemplate}
                      isLoading={!!newReplyLoading}
                      loadingText="添加中"
                      onClick={() => handleSubmit()}
                    >
                      回复
                    </Button>
                  </AuthWrapper>
                </Flex>
              </Collapse>
            </VStack>
          </Collapse>
          <Collapse
            animateOpacity
            in={showReplies}
            style={{ width: '100%', overflow: 'visible!important' }}
          >
            <VStack gap={4} w="full" pt={3}>
              {replies.map((reply) => (
                <Comment
                  poc={poc}
                  isAnnounced={isAnnounced}
                  listingSlug={listingSlug}
                  listingType={listingType}
                  defaultSuggestions={defaultSuggestions}
                  deleteComment={deleteReplyLvl1}
                  addNewReply={addNewReplyLvl1}
                  isReply
                  key={reply.id}
                  refType={refType}
                  sponsorId={sponsorId}
                  comment={reply}
                  refId={refId}
                />
              ))}
            </VStack>
          </Collapse>
        </VStack>
        <Fade
          in={(showOptions || isMobile) && comment.authorId === user?.id}
          style={{ display: 'block' }}
        >
          <Menu>
            <MenuButton>
              <button style={{ padding: '0 0.5rem' }}>
                <svg
                  width="3"
                  height="12"
                  viewBox="0 0 3 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M1.5 3C2.325 3 3 2.325 3 1.5C3 0.675 2.325 0 1.5 0C0.675 0 0 0.675 0 1.5C0 2.325 0.675 3 1.5 3ZM1.5 4.5C0.675 4.5 0 5.175 0 6C0 6.825 0.675 7.5 1.5 7.5C2.325 7.5 3 6.825 3 6C3 5.175 2.325 4.5 1.5 4.5ZM1.5 9C0.675 9 0 9.675 0 10.5C0 11.325 0.675 12 1.5 12C2.325 12 3 11.325 3 10.5C3 9.675 2.325 9 1.5 9Z"
                    fill="#94A3B8"
                  />
                </svg>
              </button>
            </MenuButton>
            <MenuList minW="10rem" px={1} py={1}>
              <MenuItem
                className="ph-no-capture"
                color="brand.slate.600"
                fontSize={{
                  base: 'sm',
                  md: 'md',
                }}
                fontWeight={500}
                onClick={deleteOnOpen}
                rounded="sm"
                tabIndex={-1}
              >
                Delete
              </MenuItem>
            </MenuList>
          </Menu>
        </Fade>
      </HStack>
      <AlertDialog
        isOpen={deleteIsOpen}
        leastDestructiveRef={cancelRef}
        onClose={deleteOnClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              删除评论
            </AlertDialogHeader>

            <AlertDialogBody>
              确定么？
              {deleteError && (
                <Alert mt={3} rounded="md" status="error">
                  <AlertIcon />
                  <VStack>
                    <AlertTitle>删除评论失败</AlertTitle>
                    <AlertDescription alignSelf="start">
                      请重试！
                    </AlertDescription>
                  </VStack>
                </Alert>
              )}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={deleteOnClose} variant="ghost">
                取消
              </Button>
              <Button
                className="ph-no-capture"
                ml={3}
                disabled={deleteLoading}
                isLoading={deleteLoading}
                onClick={handleDelete}
              >
                删除
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
};
