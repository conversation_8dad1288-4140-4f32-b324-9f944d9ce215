export const HomeIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.5 7.50002L10 1.66669L17.5 7.50002V16.6667C17.5 17.1087 17.3244 17.5326 17.0118 17.8452C16.6993 18.1578 16.2754 18.3334 15.8333 18.3334H4.16667C3.72464 18.3334 3.30072 18.1578 2.98816 17.8452C2.67559 17.5326 2.5 17.1087 2.5 16.6667V7.50002Z"
        stroke="#64748B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 18.3333V10H12.5V18.3333"
        stroke="#64748B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const LeaderboardIcon = () => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 2.75L9.25833 8.06667C9.16915 8.34278 9.0157 8.59379 8.81061 8.79904C8.60551 9.00429 8.35463 9.15794 8.07858 9.24733L2.75 11L8.06667 12.7417C8.34278 12.8308 8.59379 12.9843 8.79904 13.1894C9.00429 13.3945 9.15794 13.6454 9.24733 13.9214L11 19.25L12.7417 13.9333C12.8308 13.6572 12.9843 13.4062 13.1894 13.201C13.3945 12.9957 13.6454 12.8421 13.9214 12.7527L19.25 11L13.9333 9.25833C13.6572 9.16915 13.4062 9.0157 13.201 8.81061C12.9957 8.60551 12.8421 8.35463 12.7527 8.07858L11 2.75Z"
        stroke="#64748B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const WinnersIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_335_168)">
        <path
          d="M5.00008 7.49998H3.75008C3.19755 7.49998 2.66764 7.28049 2.27694 6.88979C1.88624 6.49908 1.66675 5.96918 1.66675 5.41665C1.66675 4.86411 1.88624 4.33421 2.27694 3.94351C2.66764 3.55281 3.19755 3.33331 3.75008 3.33331H5.00008"
          stroke="#64748B"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M15 7.49998H16.25C16.8025 7.49998 17.3324 7.28049 17.7231 6.88979C18.1138 6.49908 18.3333 5.96918 18.3333 5.41665C18.3333 4.86411 18.1138 4.33421 17.7231 3.94351C17.3324 3.55281 16.8025 3.33331 16.25 3.33331H15"
          stroke="#64748B"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M3.33325 18.3333H16.6666"
          stroke="#64748B"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.33325 12.2167V14.1667C8.33325 14.625 7.94159 14.9833 7.52492 15.175C6.54159 15.625 5.83325 16.8667 5.83325 18.3333"
          stroke="#64748B"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.6667 12.2167V14.1667C11.6667 14.625 12.0584 14.9833 12.4751 15.175C13.4584 15.625 14.1667 16.8667 14.1667 18.3333"
          stroke="#64748B"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M15 1.66669H5V7.50002C5 8.8261 5.52678 10.0979 6.46447 11.0356C7.40215 11.9732 8.67392 12.5 10 12.5C11.3261 12.5 12.5979 11.9732 13.5355 11.0356C14.4732 10.0979 15 8.8261 15 7.50002V1.66669Z"
          stroke="#64748B"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_335_168">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const AllPostsIcon = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_7_2238)">
        <path
          d="M2.66659 14.6667H13.3333C13.6869 14.6667 14.026 14.5262 14.2761 14.2762C14.5261 14.0261 14.6666 13.687 14.6666 13.3333V2.66668C14.6666 2.31305 14.5261 1.97392 14.2761 1.72387C14.026 1.47382 13.6869 1.33334 13.3333 1.33334H5.33325C4.97963 1.33334 4.64049 1.47382 4.39044 1.72387C4.14039 1.97392 3.99992 2.31305 3.99992 2.66668V13.3333C3.99992 13.687 3.85944 14.0261 3.60939 14.2762C3.35935 14.5262 3.02021 14.6667 2.66659 14.6667ZM2.66659 14.6667C2.31296 14.6667 1.97382 14.5262 1.72378 14.2762C1.47373 14.0261 1.33325 13.687 1.33325 13.3333V7.33334C1.33325 6.60001 1.93325 6.00001 2.66659 6.00001H3.99992"
          stroke="#64748B"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M12.0001 9.33334H6.66675"
          stroke="#64748B"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M10.0001 12H6.66675"
          stroke="#64748B"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M6.66675 4H12.0001V6.66667H6.66675V4Z"
          stroke="#64748B"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_7_2238">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
