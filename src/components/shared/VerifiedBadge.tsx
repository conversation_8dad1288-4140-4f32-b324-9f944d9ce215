import React from 'react';

export const VerifiedBadge = ({ style }: { style?: React.CSSProperties }) => {
  return (
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={style}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.95276 0.507061C8.8336 0.316662 8.65804 0.168151 8.45052 0.0822013C8.24301 -0.00374846 8.01385 -0.0228607 7.79496 0.0275255L6.24144 0.384369C6.08223 0.420962 5.9168 0.420962 5.75759 0.384369L4.20406 0.0275255C3.98517 -0.0228607 3.75602 -0.00374846 3.5485 0.0822013C3.34099 0.168151 3.16543 0.316662 3.04627 0.507061L2.19952 1.8584C2.11312 1.99665 1.99647 2.11329 1.85823 2.20056L0.506888 3.0473C0.316817 3.16636 0.168524 3.34163 0.0825926 3.5488C-0.00333864 3.75596 -0.0226455 3.98474 0.027352 4.20337L0.384196 5.75862C0.420656 5.91756 0.420656 6.08268 0.384196 6.24161L0.027352 7.796C-0.0228398 8.01476 -0.00363012 8.24373 0.0823104 8.45107C0.168251 8.65841 0.316653 8.83383 0.506888 8.95294L1.85823 9.79968C1.99647 9.88609 2.11312 10.0027 2.20038 10.141L3.04713 11.4923C3.29079 11.882 3.75563 12.0747 4.20406 11.9718L5.75759 11.615C5.9168 11.5784 6.08223 11.5784 6.24144 11.615L7.79583 11.9718C8.01459 12.022 8.24356 12.0028 8.4509 11.9169C8.65824 11.8309 8.83365 11.6825 8.95276 11.4923L9.79951 10.141C9.88591 10.0027 10.0026 9.88609 10.1408 9.79968L11.493 8.95294C11.6832 8.83365 11.8316 8.65804 11.9174 8.45053C12.0032 8.24303 12.0221 8.01394 11.9717 7.79514L11.6157 6.24161C11.5791 6.08241 11.5791 5.91697 11.6157 5.75776L11.9725 4.20337C12.0228 3.9847 12.0037 3.7558 11.918 3.54847C11.8322 3.34114 11.6839 3.16567 11.4939 3.04644L10.1417 2.19969C10.0036 2.11313 9.88694 1.99645 9.80037 1.8584L8.95276 0.507061ZM8.51816 4.07291C8.5716 3.97464 8.58484 3.85947 8.55509 3.75164C8.52535 3.6438 8.45494 3.55171 8.35867 3.49473C8.26241 3.43776 8.1478 3.42035 8.03896 3.44615C7.93011 3.47196 7.83553 3.53898 7.77509 3.63311L5.51566 7.4573L4.15136 6.15089C4.11088 6.10933 4.06245 6.07635 4.00896 6.05392C3.95546 6.03148 3.89799 6.02005 3.83998 6.0203C3.78197 6.02055 3.72461 6.03249 3.67131 6.05539C3.61801 6.07829 3.56987 6.11169 3.52976 6.1536C3.48965 6.19551 3.45839 6.24507 3.43785 6.29932C3.41731 6.35357 3.4079 6.41141 3.41019 6.46937C3.41248 6.52734 3.42642 6.58425 3.45119 6.63671C3.47595 6.68917 3.51102 6.73611 3.55431 6.77472L5.31175 8.45871C5.35878 8.50369 5.41537 8.53745 5.47729 8.55749C5.53921 8.57753 5.60485 8.58332 5.66932 8.57443C5.73379 8.56554 5.79542 8.5422 5.8496 8.50616C5.90378 8.47011 5.94912 8.42229 5.98223 8.36626L8.51816 4.07291Z"
        fill="#3B82F6"
      />
    </svg>
  );
};

export const VerifiedBadgeLarge = () => {
  return (
    <svg
      width="15"
      height="15"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.191 0.633868C11.042 0.395853 10.8226 0.210202 10.5632 0.102758C10.3038 -0.00468588 10.0173 -0.0285778 9.74371 0.0344091L7.8018 0.480493C7.60279 0.526236 7.396 0.526236 7.19698 0.480493L5.25508 0.0344091C4.98147 -0.0285778 4.69503 -0.00468588 4.43563 0.102758C4.17623 0.210202 3.95678 0.395853 3.80783 0.633868L2.7494 2.32315C2.6414 2.49597 2.49559 2.64178 2.32278 2.75087L0.63361 3.80938C0.396021 3.9582 0.210655 4.17731 0.103241 4.43628C-0.0041733 4.69525 -0.0283068 4.98124 0.03419 5.25456L0.480245 7.19874C0.52582 7.39742 0.52582 7.60384 0.480245 7.80252L0.03419 9.74563C-0.0285497 10.0191 -0.00453765 10.3053 0.102888 10.5645C0.210314 10.8237 0.395816 11.043 0.63361 11.1919L2.32278 12.2504C2.49559 12.3584 2.64139 12.5042 2.75048 12.677L3.80891 14.3663C4.11348 14.8534 4.69454 15.0943 5.25508 14.9658L7.19698 14.5197C7.396 14.474 7.60279 14.474 7.8018 14.5197L9.74479 14.9658C10.0182 15.0285 10.3044 15.0045 10.5636 14.8971C10.8228 14.7896 11.0421 14.6041 11.191 14.3663L12.2494 12.677C12.3574 12.5042 12.5032 12.3584 12.676 12.2504L14.3663 11.1919C14.6041 11.0428 14.7895 10.8233 14.8967 10.5639C15.004 10.3044 15.0277 10.0181 14.9646 9.74455L14.5196 7.80252C14.4739 7.6035 14.4739 7.39669 14.5196 7.19766L14.9657 5.25456C15.0285 4.9812 15.0047 4.69505 14.8974 4.43587C14.7902 4.17669 14.6049 3.95734 14.3673 3.8083L12.6771 2.74979C12.5045 2.64158 12.3587 2.49573 12.2505 2.32315L11.191 0.633868ZM10.6477 5.09146C10.7145 4.96862 10.731 4.82465 10.6939 4.68985C10.6567 4.55505 10.5687 4.43992 10.4483 4.3687C10.328 4.29748 10.1847 4.27571 10.0487 4.30797C9.91264 4.34023 9.79441 4.42402 9.71887 4.54169L6.89457 9.32223L5.1892 7.68911C5.1386 7.63716 5.07807 7.59593 5.0112 7.56788C4.94433 7.53984 4.87249 7.52554 4.79998 7.52586C4.72747 7.52618 4.65576 7.54109 4.58914 7.56972C4.52251 7.59835 4.46234 7.64011 4.4122 7.6925C4.36206 7.74489 4.32299 7.80684 4.29731 7.87466C4.27163 7.94248 4.25987 8.01478 4.26274 8.08724C4.2656 8.1597 4.28303 8.23084 4.31398 8.29642C4.34493 8.362 4.38878 8.42068 4.44289 8.46895L6.63969 10.5741C6.69848 10.6303 6.76922 10.6725 6.84661 10.6976C6.92401 10.7226 7.00607 10.7298 7.08665 10.7187C7.16724 10.7076 7.24427 10.6784 7.312 10.6334C7.37973 10.5883 7.4364 10.5285 7.47779 10.4585L10.6477 5.09146Z"
        fill="#3B82F6"
      />
    </svg>
  );
};
