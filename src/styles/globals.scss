body {
  font-family: var(--font-sans);
}

.wallet-adapter-button.wallet-adapter-button-trigger {
  width: 100% !important;
  background-color: #6366f1;
  font-size: 1rem !important;
  color: white !important;
  // border: 1px solid !important;
  border-color: #f1f5f9 !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.wallet-adapter-button.wallet-adapter-button-trigger:hover,
.wallet-adapter-button.wallet-adapter-button-trigger:active {
  background-color: #6366f1;
}

.wallet-adapter-button.wallet-adapter-button-trigger:not([disabled]):hover {
  background-color: #6366f1;
}

.wallet-adapter-button-end-icon,
.wallet-adapter-button-start-icon,
.wallet-adapter-button-end-icon img,
.wallet-adapter-button-start-icon img {
  display: none !important;
}

.bg-white {
  background-color: #fff;
}

#reset-des blockquote {
  all: revert !important;
}

#reset-des ol {
  all: revert !important;
}

#reset-des a {
  word-break: break-word;
  overflow-wrap: break-word;
  color: blue;
  text-decoration: underline;
  cursor: pointer;
}

#reset-des ul {
  all: revert !important;
}

#reset-des strong {
  all: revert !important;
}

#reset-des br {
  all: revert !important;
}

#reset-des h1 {
  all: revert !important;
}

#reset-des h2 {
  all: revert !important;
}

#reset-des h3 {
  all: revert !important;
}

#reset-des h4 {
  all: revert !important;
}

#reset-des h5 {
  all: revert !important;
}

#reset-des h6 {
  all: revert !important;
}

#reset-des code {
  white-space: pre-wrap;
  word-break: break-all;
}

#reset-des pre {
  white-space: pre-wrap;
  word-break: break-all;
}

#reset-des p {
  white-space: pre-wrap;
}

#reset-des {

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  li::marker {
    color: #475569 !important;
  }
}

#reset-des {
  * {
    font-size: 1rem !important;
  }

  h1,
  h1 * {
    font-size: 1.5rem !important;
  }

  h2,
  h2 * {
    font-size: 1.25rem !important;
  }

  h3,
  h3 * {
    font-size: 1.125rem !important;
  }

  h4,
  h4 * {
    font-size: 1rem !important;
  }

  h5,
  h5 * {
    font-size: 0.875rem !important;
  }

  h6,
  h6 * {
    font-size: 0.75rem !important;
  }
}

#reset-des {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

#reset-des * {
  color: #475569 !important;
}

.listing-description {
  >*:first-child {
    margin-top: 1rem !important; // Adjusts top padding for the first paragraph
  }
}

#reset-des {

  a,
  a * {
    color: #6366F1 !important;
  }
}

code {
  background-color: rgba(#616161, 0.1);
  color: #616161;
}

pre {
  background: #0d0d0d;
  color: #fff;
  font-family: var(--font-mono), monospace;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;

  code {
    color: inherit;
    padding: 0;
    background: none;
    font-size: 0.8rem;
  }

  blockquote {
    padding-left: 1rem;
    border-left: 2px solid rgba(#0d0d0d, 0.1);
  }

  hr {
    border: none;
    border-top: 2px solid rgba(#0d0d0d, 0.1);
    margin: 2rem 0;
  }
}

.ProseMirror {
  min-height: 40rem;
  border: 1px solid #d2d2d2 !important;
  padding: 1rem 2rem;

  &::-moz-focus-inner {
    border: "none";
    outline: "none !important";
  }
}

.tiptap {
  min-height: 100% !important;
  border: 0px !important;
  overscroll-behavior: none;
  outline: none !important;
  padding: 0rem 1rem;
}

//earn-loader-css

.earn-loader {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.earn-loader div {
  position: absolute;
  border: 4px solid #6366f1;
  opacity: 1;
  border-radius: 50%;
  animation: earn-loader 1s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.earn-loader div:nth-child(2) {
  animation-delay: -0.5s;
}

@keyframes earn-loader {
  0% {
    top: 36px;
    left: 36px;
    width: 0;
    height: 0;
    opacity: 0;
  }

  4.9% {
    top: 36px;
    left: 36px;
    width: 0;
    height: 0;
    opacity: 0;
  }

  5% {
    top: 36px;
    left: 36px;
    width: 0;
    height: 0;
    opacity: 1;
  }

  100% {
    top: 0px;
    left: 0px;
    width: 72px;
    height: 72px;
    opacity: 0;
  }
}

.tiptap img {
  margin: 0 auto;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.banner-wrapper {
  width: 100%;
  overflow: hidden;
  mask-image: linear-gradient(to right, transparent 0, black 40px, black calc(100% - 40px), transparent 100%);
}

.banner-wrapper .wrapper {
  display: flex;
  gap: 2rem;
}

.banner-wrapper .wrapper .content {
  display: flex;
  gap: 2rem;
  animation: swipe 40s linear infinite;
}

.banner-wrapper:hover .wrapper .content {
  animation-play-state: paused;
}

@keyframes swipe {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}

.sponsor-dashboard-sidebar {
  transition: width 0.3s ease-in-out;
  overflow: hidden;
}

.sponsor-dashboard-sidebar:not(.expanded) .nav-item-text {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.sponsor-dashboard-sidebar.expanded .nav-item-text {
  opacity: 1;
  transition: opacity 0.2s ease-in-out 0.1s;
}
