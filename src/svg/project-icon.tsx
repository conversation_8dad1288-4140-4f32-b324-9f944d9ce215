import React, { type CSSProperties } from 'react';

export const ProjectIcon = ({ styles }: { styles?: CSSProperties }) => {
  return (
    <svg
      width="15"
      height="15"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={styles}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.33325 4.78433C2.54056 4.78433 1.89795 5.42694 1.89795 6.21963V10.5255C1.89795 11.3182 2.54055 11.9608 3.33325 11.9608H11.0198C11.8125 11.9608 12.4551 11.3182 12.4551 10.5255V6.21963C12.4551 5.42694 11.8125 4.78433 11.0198 4.78433H3.33325ZM6.33927 5.9804C6.1411 5.9804 5.98045 6.14105 5.98045 6.33923V6.81766C5.98045 7.01583 6.1411 7.17649 6.33927 7.17649H8.01379C8.21196 7.17649 8.37261 7.01583 8.37261 6.81766V6.33923C8.37261 6.14105 8.21196 5.9804 8.01379 5.9804H6.33927Z"
        fill="parent"
      />
      <path
        opacity="0.3"
        d="M5.98039 4.78435H4.7843V4.18631C4.7843 3.19544 5.58756 2.39218 6.57843 2.39218H7.77451C8.76538 2.39218 9.56864 3.19544 9.56864 4.18631V4.78435H8.37255V4.18631C8.37255 3.85602 8.1048 3.58826 7.77451 3.58826H6.57843C6.24814 3.58826 5.98039 3.85602 5.98039 4.18631V4.78435Z"
        fill="parent"
      />
    </svg>
  );
};
