generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model Bounties {
  id                   String             @id @default(uuid())
  title                String
  slug                 String             @unique
  description          String?            @db.Text
  deadline             DateTime?
  eligibility          Json?
  status               status             @default(OPEN)
  token                String?
  rewardAmount         Float?
  rewards              Json?
  maxBonusSpots        Int                @default(0)
  usdValue             Float?
  sponsorId            String
  pocId                String
  source               Source             @default(NATIVE)
  isPublished          Boolean            @default(false)
  isFeatured           Boolean            @default(false)
  isActive             Boolean            @default(true)
  isArchived           Boolean            @default(false)
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt
  applicationLink      String?
  skills               Json?
  type                 BountyType         @default(bounty)
  requirements         String?            @db.Text
  totalPaymentsMade    Int?               @default(0)
  totalWinnersSelected Int?               @default(0)
  isWinnersAnnounced   <PERSON>olean            @default(false)
  isLockedPayment      Boolean            @default(false)
  templateId           String?
  region               String             @default("GLOBAL")
  pocSocials           String?
  hackathonprize       Boolean            @default(false)
  applicationType      ApplicationType    @default(fixed)
  timeToComplete       String?
  references           Json?
  referredBy           String?
  publishedAt          DateTime?
  isPrivate            Boolean            @default(false)
  hackathonId          String?
  compensationType     CompensationType   @default(fixed)
  maxRewardAsk         Int?
  minRewardAsk         Int?
  template             BountiesTemplates? @relation(fields: [templateId], references: [id])
  sponsor              Sponsors           @relation(fields: [sponsorId], references: [id])
  poc                  User               @relation("poc", fields: [pocId], references: [id])
  language             String?
  shouldSendEmail      Boolean            @default(true)
  isFndnPaying         Boolean            @default(false)
  Submission           Submission[]
  SubscribeBounty      SubscribeBounty[]
  Hackathon            Hackathon?         @relation(fields: [hackathonId], references: [id])
  winnersAnnouncedAt   DateTime?
  Scouts               Scouts[]
  Comments             Comment[]

  @@index([id, slug])
  @@index([sponsorId])
  @@index([pocId])
  @@index([templateId])
  @@index([hackathonId])
  @@index([isPublished, isPrivate])
  @@index([deadline, updatedAt])
  @@index([isWinnersAnnounced])
  @@index([title])
  @@index([deadline(sort: Asc)], map: "Bounties_deadline_asc_idx")
  @@index([deadline(sort: Desc)], map: "Bounties_deadline_desc_idx")
  @@index([winnersAnnouncedAt(sort: Desc)])
  @@index([isFeatured(sort: Desc)])
}

model BountiesTemplates {
  id               String           @id @default(uuid())
  title            String?
  deadline         DateTime?
  slug             String?
  description      String?          @db.Text
  color            String?
  emoji            String?
  isFeatured       Boolean          @default(false)
  isActive         Boolean          @default(true)
  isArchived       Boolean          @default(false)
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  skills           Json?
  type             BountyType       @default(bounty)
  requirements     String?          @db.Text
  region           Regions          @default(GLOBAL)
  applicationType  ApplicationType  @default(fixed)
  status           status           @default(OPEN)
  timeToComplete   String?
  token            String?
  references       Json?
  referredBy       String?
  publishedAt      DateTime?
  compensationType CompensationType @default(fixed)
  maxRewardAsk     Int?
  minRewardAsk     Int?
  language         String?
  rewardAmount     Float?
  rewards          Json?
  maxBonusSpots    Int              @default(0)
  usdValue         Float?
  sponsorId        String
  pocId            String
  pocSocials       String?
  source           Source           @default(NATIVE)
  isPublished      Boolean          @default(false)
  Bounties         Bounties[]
  sponsor          Sponsors         @relation(fields: [sponsorId], references: [id])
  poc              User             @relation("poc", fields: [pocId], references: [id])

  @@index([isActive, isArchived, type])
  @@index([pocId])
  @@index([sponsorId])
  @@index([type])
}

model Comment {
  id               String            @id @default(uuid())
  message          String            @db.VarChar(1000)
  authorId         String
  refType          CommentRefType    @default(BOUNTY)
  refId            String
  isActive         Boolean           @default(true)
  isArchived       Boolean           @default(false)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  replyToId        String?
  submissionId     String?
  type             CommentType       @default(NORMAL)
  author           User              @relation(fields: [authorId], references: [id])
  repliedTo        Comment?          @relation("CommentReplies", fields: [replyToId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  replies          Comment[]         @relation("CommentReplies")
  listing          Bounties?         @relation(fields: [refId], references: [id], map: "Comment_Listing_fkey")
  submission       Submission?       @relation(fields: [refId], references: [id], map: "Comment_Submission_fkey")
  pow              PoW?              @relation(fields: [refId], references: [id], map: "Comment_PoW_fkey")
  GrantApplication GrantApplication? @relation(fields: [refId], references: [id], map: "Comment_GrantApplication_fkey")

  @@index([id, refId])
  @@index([authorId])
  @@index([replyToId])
  @@index([refId])
}

model Grants {
  id                     String             @id @default(uuid())
  title                  String
  slug                   String             @unique
  description            String?            @db.Text
  shortDescription       String?            @db.VarChar(1000)
  token                  String?
  minReward              Float?
  maxReward              Float?
  totalPaid              Float              @default(0)
  totalApproved          Float              @default(0)
  historicalApplications Int                @default(0)
  link                   String?
  sponsorId              String
  pocId                  String
  isPublished            Boolean            @default(false)
  isFeatured             Boolean            @default(false)
  isActive               Boolean            @default(true)
  isArchived             Boolean            @default(false)
  createdAt              DateTime           @default(now())
  updatedAt              DateTime           @updatedAt
  skills                 Json?
  logo                   String?
  region                 Regions            @default(GLOBAL)
  sponsor                Sponsors           @relation(fields: [sponsorId], references: [id])
  poc                    User               @relation(fields: [pocId], references: [id])
  questions              Json?
  pocSocials             String?
  status                 GrantStatus        @default(OPEN)
  GrantApplication       GrantApplication[]
  airtableId             String?
  avgResponseTime        String?            @default("24h")
  isNative               Boolean            @default(false)
  isPrivate              Boolean            @default(false)
  references             Json?

  @@index([id, slug])
  @@index([pocId])
  @@index([sponsorId])
}

model GrantApplication {
  id                  String                 @id @default(uuid())
  userId              String
  grantId             String
  applicationStatus   GrantApplicationStatus @default(Pending)
  projectTitle        String
  projectOneLiner     String
  projectDetails      String                 @db.Text
  projectTimeline     String
  proofOfWork         String                 @db.Text
  walletAddress       String
  twitter             String?
  milestones          String?                @db.Text
  kpi                 String?                @db.Text
  answers             Json?
  createdAt           DateTime               @default(now())
  updatedAt           DateTime               @updatedAt
  user                User                   @relation(fields: [userId], references: [id])
  grant               Grants                 @relation(fields: [grantId], references: [id])
  label               SubmissionLabels       @default(Unreviewed)
  ask                 Float                  @default(0)
  approvedAmount      Float                  @default(0)
  approvedAmountInUSD Float                  @default(0)
  decidedAt           DateTime?
  totalPaid           Float                  @default(0)
  isShipped           Boolean                @default(false)
  paymentDetails      Json?
  totalTranches       Int                    @default(0)
  like                Json?
  likeCount           Int                    @default(0)
  Comments            Comment[]

  @@index([userId])
  @@index([grantId])
  @@index([likeCount])
}

model Submission {
  id                 String           @id @default(uuid())
  link               String?          @db.VarChar(500)
  tweet              String?          @db.VarChar(500)
  status             SubmissionStatus @default(Pending)
  eligibilityAnswers Json?
  userId             String
  listingId          String
  isWinner           Boolean          @default(false)
  winnerPosition     Int?
  isActive           Boolean          @default(true)
  isArchived         Boolean          @default(false)
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  like               Json?
  likeCount          Int              @default(0)
  isPaid             Boolean          @default(false)
  paymentDetails     Json?
  otherInfo          String?          @db.Text
  ask                Int?
  label              SubmissionLabels @default(Unreviewed)
  rewardInUSD        Float            @default(0)
  listing            Bounties         @relation(fields: [listingId], references: [id])
  user               User             @relation(fields: [userId], references: [id])
  ogImage            String?          @db.Text
  notes              String?          @db.VarChar(500)
  Comments           Comment[]

  @@index([id, listingId])
  @@index([userId])
  @@index([listingId])
  @@index([isWinner])
  @@index([createdAt, isWinner])
  @@index([createdAt, listingId])
  @@index([likeCount])
}

model Sponsors {
  id           String              @id @default(uuid())
  name         String              @unique
  slug         String              @unique
  logo         String?
  url          String?
  industry     String
  twitter      String?
  telegram     String?
  wechat       String?
  bio          String?
  createdAt    DateTime            @default(now())
  updatedAt    DateTime            @updatedAt
  isArchived   Boolean             @default(false)
  isActive     Boolean             @default(false)
  entityName   String?
  isVerified   Boolean             @default(false)
  isCaution    Boolean             @default(false)
  st           Boolean             @default(false)
  Hackathon    Hackathon?
  UserSponsors UserSponsors[]
  Bounties     Bounties[]
  Templates    BountiesTemplates[]
  Grants       Grants[]
  User         User[]
  UserInvites  UserInvites[]

  @@index([id, slug])
}

model Account {
  id                String  @id @default(uuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(uuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model User {
  id                 String               @id @default(uuid())
  publicKey          String?
  email              String               @unique
  username           String?              @unique
  photo              String?              @db.Text
  firstName          String?              @db.Text
  lastName           String?              @db.Text
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  isVerified         Boolean              @default(false)
  role               String               @default("USER")
  isTalentFilled     Boolean              @default(false)
  interests          String?              @db.Text
  bio                String?              @db.Text
  twitter            String?              @db.Text
  discord            String?              @db.Text
  github             String?              @db.Text
  linkedin           String?              @db.Text
  website            String?              @db.Text
  telegram           String?              @db.Text
  wechat             String?              @db.Text
  community          String?              @db.Text
  experience         String?
  superteamLevel     String?
  location           String?
  cryptoExperience   String?
  workPrefernce      String?
  currentEmployer    String?
  notifications      Json?
  private            Boolean              @default(false)
  skills             Json?
  currentSponsorId   String?
  emailVerified      DateTime?
  hackathonId        String?
  featureModalShown  Boolean              @default(false)
  surveysShown       Json?
  stRecommended      Boolean              @default(false)
  acceptedTOS        Boolean              @default(false)
  stLead             String?
  currentSponsor     Sponsors?            @relation(fields: [currentSponsorId], references: [id])
  UserSponsors       UserSponsors[]
  poc                Bounties[]           @relation("poc")
  templates          BountiesTemplates[]  @relation("poc")
  Comment            Comment[]
  Submission         Submission[]
  Grants             Grants[]
  UserInvites        UserInvites[]
  SubscribeBounty    SubscribeBounty[]
  PoW                PoW[]
  accounts           Account[]
  sessions           Session[]
  Hackathon          Hackathon?           @relation(fields: [hackathonId], references: [id])
  emailSettings      EmailSettings[]
  TalentRankings     TalentRankings[]
  Scouts             Scouts[]
  GrantApplication   GrantApplication[]
  SubscribeHackathon SubscribeHackathon[]
  isBlocked          Boolean              @default(false)

  @@index([email, publicKey, username])
  @@index([currentSponsorId])
  @@index([hackathonId])
}

model Scouts {
  id            String   @id @default(uuid())
  userId        String
  listingId     String
  dollarsEarned Int
  score         Decimal  @db.Decimal(9, 2)
  invited       Boolean
  skills        Json
  createdAt     DateTime @default(now())
  listing       Bounties @relation(fields: [listingId], references: [id])
  user          User     @relation(fields: [userId], references: [id])

  @@unique([userId, listingId])
  @@index([listingId])
}

model TalentRankings {
  id               String                 @id @default(uuid())
  userId           String
  skill            TalentRankingSkills    @default(ALL)
  timeframe        TalentRankingTimeframe @default(ALL_TIME)
  rank             Int
  submissions      Int                    @default(0)
  winRate          Int                    @default(0)
  wins             Int                    @default(0)
  totalEarnedInUSD Int
  user             User                   @relation(fields: [userId], references: [id])

  @@unique([userId, skill, timeframe])
  @@index([skill, timeframe])
}

model PoW {
  id          String    @id @default(uuid())
  userId      String
  title       String
  description String    @db.Text
  skills      Json?
  link        String
  createdAt   DateTime  @default(now())
  subSkills   Json?
  updatedAt   DateTime  @updatedAt
  like        Json?
  likeCount   Int       @default(0)
  user        User      @relation(fields: [userId], references: [id])
  ogImage     String?   @db.Text
  Comments    Comment[]

  @@index([userId])
  @@index([createdAt])
  @@index([likeCount])
}

model EmailSettings {
  id       Int    @id @default(autoincrement())
  userId   String
  category String
  user     User   @relation(fields: [userId], references: [id])

  @@index([userId, category])
}

model UserSponsors {
  userId    String
  sponsorId String
  role      Role     @default(MEMBER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])
  sponsor   Sponsors @relation(fields: [sponsorId], references: [id])

  @@id([userId, sponsorId])
  @@index([sponsorId])
}

model UserInvites {
  id         String   @id @default(uuid())
  email      String
  senderId   String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  sponsorId  String
  memberType Role     @default(MEMBER)
  token      String   @unique
  expires    DateTime
  sender     User     @relation(fields: [senderId], references: [id])
  sponsor    Sponsors @relation(fields: [sponsorId], references: [id])

  @@index([senderId])
  @@index([sponsorId])
}

model emailLogs {
  id        String    @id @default(uuid())
  email     String?
  type      EmailType
  bountyId  String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model ResendLogs {
  id        String   @id @default(uuid())
  email     String
  subject   String
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SubscribeBounty {
  id         String    @id @default(uuid())
  userId     String
  bountyId   String
  isArchived Boolean   @default(false)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  User       User      @relation(fields: [userId], references: [id])
  Bounties   Bounties? @relation(fields: [bountyId], references: [id])

  @@index([bountyId])
  @@index([userId])
}

model SubscribeHackathon {
  id          String     @id @default(uuid())
  userId      String
  hackathonId String
  isArchived  Boolean    @default(false)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  User        User       @relation(fields: [userId], references: [id])
  Hackthons   Hackathon? @relation(fields: [hackathonId], references: [id])

  @@index([hackathonId])
  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime
  createdAt  DateTime @default(now())

  @@unique([identifier, token])
  // 🚀 性能优化索引
  @@index([identifier, createdAt])  // 查询最新验证码 (orderBy createdAt desc)
  @@index([identifier, expires])   // 查询未过期验证码 (where expires > now)
  @@index([expires])               // 清理过期验证码任务 (where expires < now)
}

model Hackathon {
  id                 String               @id @default(uuid())
  slug               String               @unique
  name               String
  logo               String
  sponsorId          String?              @unique
  deadline           DateTime?
  startDate          DateTime?
  description        String
  altLogo            String?
  announceDate       DateTime?
  eligibility        Json?
  listings           Bounties[]
  Sponsor            Sponsors?            @relation(fields: [sponsorId], references: [id])
  User               User[]
  SubscribeHackathon SubscribeHackathon[]
}

model UnsubscribedEmail {
  id        String   @id @default(uuid())
  email     String   @unique
  createdAt DateTime @default(now())
}

model BlockedEmail {
  id        String   @id @default(uuid())
  email     String   @unique
  createdAt DateTime @default(now())
}

enum TalentRankingSkills {
  DEVELOPMENT
  DESIGN
  CONTENT
  OTHER
  ALL
}

enum EmailType {
  BOUNTY_REVIEW
  BOUNTY_DEADLINE
  BOUNTY_DEADLINE_WEEK
  BOUNTY_CLOSE_DEADLINE
  NO_VERIFICATION
  NO_ACTIVITY
  NO_REVIEW_SPONSOR_1
  NO_REVIEW_SPONSOR_2
  ROLLING_15_DAYS
  ROLLING_30_DAYS
  NEW_LISTING
  ROLLING_UNPUBLISH
}

enum Role {
  ADMIN
  MEMBER
}

enum Source {
  NATIVE
  IMPORT
}

enum ApplicationType {
  rolling
  fixed
}

enum status {
  OPEN
  REVIEW
  CLOSED
  VERIFYING
  VERIFY_FAIL
  PREVIEW
}

enum GrantStatus {
  OPEN
  CLOSED
}

enum GrantApplicationStatus {
  Pending
  Approved
  Completed
  Rejected
}

enum BountyType {
  bounty
  project
  hackathon
}

enum CommentRefType {
  BOUNTY
  SUBMISSION
  GRANT_APPLICATION
  POW
}

enum CommentType {
  NORMAL
  SUBMISSION
  DEADLINE_EXTENSION
  WINNER_ANNOUNCEMENT
}

enum JobType {
  parttime
  fulltime
  internship
}

enum Regions {
  GLOBAL
  CHINA
}

enum CompensationType {
  fixed
  range
  variable
}

enum SubmissionLabels {
  Unreviewed
  Reviewed
  Shortlisted
  Spam
}

enum TalentRankingTimeframe {
  THIS_YEAR
  LAST_30_DAYS
  LAST_7_DAYS
  ALL_TIME
}

enum SubmissionStatus {
  Pending
  Approved
  Rejected
}
