{
  "extends": [
    "next/core-web-vitals", // Needed to avoid warning in next.js build: 'The Next.js plugin was not detected in your ESLint configuration'
    "plugin:prettier/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:@tanstack/eslint-plugin-query/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@tanstack/query"],
  "rules": {
    "@typescript-eslint/consistent-type-imports": [
      "error",
      {
        "prefer": "type-imports",
        "fixStyle": "inline-type-imports"
      }
    ],
    "@tanstack/query/exhaustive-deps": "error",
    "@tanstack/query/no-rest-destructuring": "error",
    "@tanstack/query/stable-query-client": "error"
  },
  "overrides": [
    // Configuration for TypeScript files
    {
      "files": ["**/*.ts", "**/*.tsx"],
      "plugins": [
        "@typescript-eslint/eslint-plugin",
        "@typescript-eslint",
        "unused-imports",
        "simple-import-sort",
        "chakra-ui",
        "react",
        "react-hooks",
        "@tanstack/query"
      ],
      "extends": [
        "plugin:@typescript-eslint/recommended",
        "next/core-web-vitals",
        "plugin:prettier/recommended",
        "plugin:@tanstack/eslint-plugin-query/recommended"
      ],
      "parserOptions": {
        "project": "./tsconfig.json"
      },
      "rules": {
        "no-restricted-imports": [
          "error",
          {
            "patterns": [
              {
                "group": ["@/features/*/*"],
                "message": "Import from '@features' should not go beyond the first level of nesting."
              }
            ]
          }
        ],
        "import/no-duplicates": ["error", { "prefer-inline": true }],
        "react-hooks/exhaustive-deps": "off", // Incorrectly report needed dependency with Next.js router
        "@next/next/no-img-element": "off", // We currently not using next/image because it isn't supported with SSG mode
        "@typescript-eslint/consistent-type-imports": "error", // Ensure `import type` is used when it's necessary
        "simple-import-sort/imports": "error", // Import configuration for `eslint-plugin-simple-import-sort`
        "simple-import-sort/exports": "error", // Export configuration for `eslint-plugin-simple-import-sort`
        "@typescript-eslint/no-unused-vars": "off",
        "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
        "unused-imports/no-unused-imports": "error",
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-var-requires": "off",
        "unused-imports/no-unused-vars": [
          "error",
          { "argsIgnorePattern": "^_" }
        ],
        "chakra-ui/props-order": "error",
        "chakra-ui/props-shorthand": "error",
        "chakra-ui/require-specific-component": "error",
        "@tanstack/query/exhaustive-deps": "error",
        "@tanstack/query/no-rest-destructuring": "error",
        "@tanstack/query/stable-query-client": "error"
      }
    }
  ]
}
